import {  useUser } from '@clerk/clerk-expo'
import { Text, View, Alert, Image, FlatList, TouchableOpacity} from 'react-native'
import { SignOutButton } from '@/components/SignOutButton'
import { useTransactions } from '@/hooks/useTransactions'
import { useEffect } from 'react'
import PageLoader from '@/components/PageLoader'
import {styles} from '../../assets/styles/home.styles'
import { useRouter } from 'expo-router'
import { COLORS } from '@/constants/colors'
import { Ionicons } from '@expo/vector-icons'
import { BalanceCard } from '../../components/BalanceCard'
import { TransactionItem } from '../../components/TransactionItem'
import { NoTransactionsFound } from '../../components/NoTransactionsFound'
import { RefreshControl } from 'react-native'
import { useState } from 'react'




export default function Page() {
  const { user } = useUser();
  const router= useRouter ();

  const [refreshing, setRefreshing] = useState(false);

  const {transactions, summary, isLoading, deleteTransaction, loadData,}= useTransactions(user?.id);

  const onRefresh = async () => {
  setRefreshing(true);
  await loadData();
  setRefreshing(false);
    };

  useEffect(() => {
    loadData();
  }, [loadData]); 


  const handleDelete = async (id) => {
    Alert.alert(
      'Delete transaction',
      'Are you sure you want to delete this transaction?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Delete',
          onPress: async () => {
            try {
              await deleteTransaction(id);
              Alert.alert('Transaction deleted successfully');
            } catch (error) {
              console.error('Error deleting transaction', error);
              Alert.alert('Failed to delete transaction');
            }
          },
        },
      ],  
)
    await deleteTransaction(id);
  };


  if (isLoading && !refreshing) {
    return <PageLoader />
  }


  console.log("userId",user.id);
  console.log("transactions",transactions);
  console.log("summary",summary);

  return (
    <View style={styles.container}>
      <View style={styles.content}>
        {/*Header*/}
        <View style={styles.header}>
          {/*Left side*/}
          <View style={styles.headerLeft}>
            <Image
              source={require('../../assets/images/revenue-2.png')}
              style={styles.headerLogo}
              resizeMode="contain"
            />
            <View style={styles.welcomeContainer}>
            <Text style={styles.welcomeText}>Welcome</Text>
            <Text style={styles.usernameText}>
              {user?.emailAddresses[0].emailAddress.split('@')[0]}
            </Text>
            </View>
          </View>
          {/*Right side*/}
          <View style={styles.headerRight}>
            <TouchableOpacity style={styles.addButton} onPress={() => router.push('/create')}>
              <Ionicons name="add-circle" size={24} color={COLORS.white} />
              <Text style={styles.addButtonText}>Add</Text>
            </TouchableOpacity>
            <SignOutButton />
          </View>
        </View>
        <BalanceCard summary={summary} />
        {/*Transactions*/}
        <View style={styles.transactionsContainer}>
          <View style={styles.transactionsHeaderContainer}>
            <Text style={styles.sectionTitle}> Recent Transactions</Text>
          </View>
          <View style={styles.transactionsList}>
            <View style={styles.transactionsListContent}>
              {transactions.map((transaction) => (
                <TransactionItem
                  key={transaction.id}
                  transaction={transaction}
                  deleteTransaction={deleteTransaction}
                />
              ))}
            </View>
          </View>
        </View>

      </View>
      <FlatList
        style={styles.transactionsList}
        contentContainerStyle={styles.transactionsListContent}
        data={transactions}
        renderItem={({ item }) => (
          <TransactionItem
            item ={item.id}
            onDelete={handleDelete}
          />)}
          ListEmptyComponent={<NoTransactionsFound/>}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />}
        />
    </View>
  );
}