import express from 'express';
import dotenv from 'dotenv';
import cors from 'cors';
import {initDB} from './config/db.js';
import rateLimiter from './middleware/rateLimiter.js';
import transactionsRoute from './routes/transactionsRoute.js';
import job from "./config/cron.js";


dotenv.config();

const app = express();

if (process.env.NODE_ENV === "production") job.start();

// middleware req ve server arasındaki bağlantı

app.use(cors());
app.use(express.json());
app.use(rateLimiter);


// our custom middleware
// app.use((req,res,next) =>  {
//     console.log('Request received',req.method);
//     next();
// });

const PORT = process.env.PORT || 5001;

app.get("/api/health",(req,res) => {
    res.status(200).json({status: "ok"});
});



app.use('/api/transactions', transactionsRoute);
app.use('/api/products', transactionsRoute);

initDB().then(() => {
    app.listen(PORT, () => {
        console.log('Server is up and runnig on port:', PORT);
    });
});
