import React from 'react'
import { View, Text, TouchableOpacity } from 'react-native'
import {styles} from '../assets/styles/home.styles'
import { COLORS } from '../constants/colors'
import { Ionicons } from '@expo/vector-icons'
import { useTransactions } from '../hooks/useTransactions'
import { formatDate } from '../lib/utils'

const CATEGORY_ICONS = {
    "Food & Drinks": "fast-food",
    Shopping: "cart",
  Income: 'cash',
  Expense: 'cash',
  Transfer: 'swap-horizontal',
  Entertainment: 'game-controller',
  Transportation: 'car',
  Health: 'medkit',
  Education: 'book',
  Housing: 'home',
  Utilities: 'flash',
  Insurance: 'shield',
  Debt: 'warning',
  Savings: 'save',
  Gifts: 'gift',
  Taxes: 'warning',
  Other: 'ellipsis-horizontal',

};




export const TransactionItem = ({ item, onDelete }) => {
  const isIncome = parseFloat(item.amount) > 0;
  const iconname = CATEGORY_ICONS[item.category] || "pricetag-outline";

  return (
    <View style={styles.transactionCard} key={item.id}>
      <TouchableOpacity style={styles.transactionContent}>
        <View style={styles.categoryIconContainer}>
          <Ionicons name={iconname} size={24} color={isIncome ? COLORS.income : COLORS.expense } />
        </View>
        <View style={styles.transactionLeft}>
          <Text style={styles.transactionTitle}>{item.title}</Text>
          <Text style={styles.transactionCategory}>{item.category}</Text>
        </View>
        <View style={styles.transactionRight}>
          <Text style={[styles.transactionAmount, {color: isIncome ? COLORS.income : COLORS.expense}]}>
            {isIncome ? '+' : '-'}${Math.abs(parseFloat(item.amount)).toFixed(2)}
          </Text>
          <Text style={styles.transactionDate}>{formatDate(item.create_at)}</Text>
        </View>   
      </TouchableOpacity>    
      <TouchableOpacity style={styles.deleteButton} onPress={() => onDelete(item.id)}>
        <Ionicons name="trash-outline" size={24} color={COLORS.text} />
      </TouchableOpacity>
    </View>
  )
}


  